// StaffUserReports specific styling
.report-main-container {
  // Fix checkbox styling to show primary color when selected
  .table-checkbox {
    &.MuiCheckbox-root {
      color: var(--radio-btn-uncheck-color-primary);

      &.Mui-checked {
        color: var(--color-primary) !important;

        svg {
          fill: var(--color-primary) !important;
        }
      }

      &:hover {
        background-color: var(--color-primary-opacity);
      }

      svg {
        width: var(--check-box-icon-size);
        height: var(--check-box-icon-size);
      }
    }
  }

  // Fix ID column text color to use primary color instead of black
  .common-table-container {
    .title-text {
      color: var(--color-primary) !important;

      &.cursor-pointer {
        &:hover {
          color: var(--color-primary) !important;
          text-decoration: underline;
        }
      }
    }

    // Ensure table header checkbox also uses primary color
    .MuiTableCell-head {
      .table-checkbox {
        &.MuiCheckbox-root {
          color: var(--text-color-white);

          &.Mui-checked {
            color: var(--text-color-white) !important;

            svg {
              fill: var(--text-color-white) !important;
            }
          }

          &:hover {
            background-color: rgba(255, 255, 255, 0.1);
          }
        }
      }

      .title-text {
        color: var(--text-color-white) !important;
      }
    }
  }

  // Export section styling to match original Staff component
  .export-section {
    display: flex;

    .custom-button-wrapper {
      .MuiButtonBase-root {
        border: 0 !important;
        padding: 8px;
        min-width: 0;
      }
    }
  }
}

// Export popover styling to match original Staff component
.export-popover {
  .MuiPaper-root {
    width: 120px !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .export-option {
    p {
      text-align: center;
    }
  }
}
